package com.djcps.ai.aibackend.tools;

import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Service
@RequiredArgsConstructor
public class GenerateService {
    private final ChatClient chatClient;

    public Flux<String> generateStream(String systemPrompt, String customPrompt) {
        return chatClient
                .prompt(systemPrompt)
                .user(customPrompt)
                .stream().content();
    }

}
