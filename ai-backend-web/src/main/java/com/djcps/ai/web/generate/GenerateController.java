package com.djcps.ai.web.generate;

import com.djcps.ai.aibackend.tools.GenerateService;
import com.djcps.ai.aibackend.tools.vo.GeneVo;
import com.djcps.ai.service.system.SystemConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("/generate")
@Slf4j
@RequiredArgsConstructor
public class GenerateController {

    private final GenerateService generateService;
    private final SystemConfigService systemConfigService;
    @PostMapping("/corn")
    public Flux<String> geneCorn(@RequestBody GeneVo geneVo) {
        String prompt = systemConfigService.findValueByKeyDefault(geneVo.getKey(), "根据用户输入生成标准的corn表达式");
        return generateService.generateStream(prompt,geneVo.getContent());
    }
}
