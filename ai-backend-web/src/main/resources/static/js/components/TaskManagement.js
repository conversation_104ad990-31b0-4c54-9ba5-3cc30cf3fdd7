// TaskManagement.js - 任务管理组件
const { useState, useEffect } = React;

function TaskManagement() {
    // 状态管理
    const [tasks, setTasks] = useState([]);
    const [templates, setTemplates] = useState([]);
    const [loading, setLoading] = useState(true);
    const [templatesLoading, setTemplatesLoading] = useState(false);
    const [selectedTask, setSelectedTask] = useState(null);
    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState({
        title: '',
        remark: '',
        templateId: '',
        cornExp: '0 0 9 * * ?'  // 默认每天9点执行
    });

    // 获取任务列表
    useEffect(() => {
        fetchTaskList();
        fetchTemplateList();
    }, []);

    // 从后端获取任务列表
    const fetchTaskList = async () => {
        try {
            setLoading(true);
            const response = await fetch('http://172.19.50.34:8080/ai-backend/task/list', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const result = await response.json();

            // 检查响应格式
            if (result.success && result.data) {
                // 将后端数据格式转换为前端格式
                const mappedTasks = result.data.map(task => ({
                    id: task.id,
                    title: task.title || '',
                    remark: task.remark || '',
                    templateId: task.templateId || '',
                    cornExp: task.cornExp || '',
                    userId: task.userId || '',
                    createdAt: task.createTime ? new Date(task.createTime).toLocaleDateString() : '',
                    updatedAt: task.createTime ? new Date(task.createTime).toLocaleDateString() : ''
                }));

                setTasks(mappedTasks);
            } else {
                console.error('Invalid response format:', result);
                setTasks([]);
            }
        } catch (err) {
            console.error('Error fetching task list:', err);
            setTasks([]);
            window.showMessage.error('获取任务列表失败: ' + err.message);
        } finally {
            setLoading(false);
        }
    };

    // 从后端获取模板列表
    const fetchTemplateList = async () => {
        try {
            setTemplatesLoading(true);
            const response = await fetch('http://172.19.50.34:8080/ai-backend/template/list', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                setTemplates(result.data);
            } else {
                console.error('Invalid template response format:', result);
                setTemplates([]);
            }
        } catch (err) {
            console.error('Error fetching template list:', err);
            setTemplates([]);
        } finally {
            setTemplatesLoading(false);
        }
    };

    // 选择任务
    const handleSelectTask = (task) => {
        setSelectedTask(task);
        setIsEditing(false);
        setFormData({
            title: task.title,
            remark: task.remark,
            templateId: task.templateId,
            cornExp: task.cornExp
        });
    };

    // 创建新任务
    const handleCreateNew = () => {
        setFormData({
            title: '',
            remark: '',
            templateId: '',
            cornExp: '0 0 9 * * ?'
        });
        setSelectedTask(null);
        setIsEditing(true);
    };

    // 编辑任务
    const handleEdit = () => {
        if (!selectedTask) return;
        
        setFormData({
            title: selectedTask.title,
            remark: selectedTask.remark,
            templateId: selectedTask.templateId,
            cornExp: selectedTask.cornExp
        });
        setIsEditing(true);
    };

    // 取消编辑
    const handleCancel = () => {
        setIsEditing(false);
        if (selectedTask) {
            // 如果是编辑现有任务，恢复选中状态
            setSelectedTask(selectedTask);
        }
    };

    // 处理表单输入变化
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // 保存任务
    const handleSave = async () => {
        // 验证表单
        if (!formData.title.trim()) {
            window.showMessage.warning('任务标题不能为空');
            return;
        }

        if (!formData.templateId) {
            window.showMessage.warning('请选择关联模板');
            return;
        }

        if (!formData.cornExp.trim()) {
            window.showMessage.warning('Cron表达式不能为空');
            return;
        }

        try {
            // 构建要保存的任务数据
            const taskData = {
                id: selectedTask ? selectedTask.id : null,
                title: formData.title,
                remark: formData.remark,
                templateId: parseInt(formData.templateId),
                cornExp: formData.cornExp
            };

            const response = await fetch('http://172.19.50.34:8080/ai-backend/task/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(taskData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                // 将后端返回的数据转换为前端格式
                const savedTask = {
                    id: result.data.id,
                    title: result.data.title || '',
                    remark: result.data.remark || '',
                    templateId: result.data.templateId || '',
                    cornExp: result.data.cornExp || '',
                    userId: result.data.userId || '',
                    createdAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : '',
                    updatedAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : ''
                };

                // 重新加载任务列表以确保数据同步
                await fetchTaskList();

                // 设置选中的任务
                setSelectedTask(savedTask);
                setIsEditing(false);
                window.showMessage.success('任务保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存任务失败:', error);
            window.showMessage.error('保存任务失败: ' + error.message);
        }
    };

    // 删除任务
    const handleDelete = async () => {
        if (!selectedTask) return;

        const confirmed = await window.showConfirm(`确定要删除任务 "${selectedTask.title}" 吗？`);
        if (confirmed) {
            try {
                const response = await fetch(`http://172.19.50.34:8080/ai-backend/task/delete/${selectedTask.id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    // 重新加载任务列表以确保数据同步
                    await fetchTaskList();
                    setSelectedTask(null);
                    window.showMessage.success('任务删除成功！');
                } else {
                    throw new Error(result.msg || '删除失败');
                }
            } catch (error) {
                console.error('删除任务失败:', error);
                window.showMessage.error('删除任务失败: ' + error.message);
            }
        }
    };

    // 获取模板名称
    const getTemplateName = (templateId) => {
        const template = templates.find(t => t.id === templateId);
        return template ? template.title : '未知模板';
    };

    // 常用Cron表达式预设
    const cronPresets = [
        { label: '每天9点', value: '0 0 9 * * ?' },
        { label: '每天18点', value: '0 0 18 * * ?' },
        { label: '每周一9点', value: '0 0 9 ? * MON' },
        { label: '每月1号9点', value: '0 0 9 1 * ?' },
        { label: '每小时', value: '0 0 * * * ?' },
        { label: '每30分钟', value: '0 */30 * * * ?' }
    ];

    // 渲染任务详情
    const renderTaskDetail = () => {
        if (!selectedTask) {
            return (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                    </svg>
                    <p>选择一个任务查看详情</p>
                </div>
            );
        }

        return (
            <div className="h-full flex flex-col">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-semibold">{selectedTask.title}</h3>
                    <div className="flex space-x-2">
                        <button
                            onClick={handleEdit}
                            className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                        >
                            编辑
                        </button>
                        <button
                            onClick={handleDelete}
                            className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm"
                        >
                            删除
                        </button>
                    </div>
                </div>

                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">任务描述:</label>
                        <p className="text-gray-600">{selectedTask.remark || '无描述'}</p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">关联模板:</label>
                        <p className="text-gray-600">{getTemplateName(selectedTask.templateId)}</p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Cron表达式:</label>
                        <p className="text-gray-600 font-mono">{selectedTask.cornExp}</p>
                    </div>

                    <div className="text-sm text-gray-500">
                        <p>创建于: {selectedTask.createdAt}</p>
                        <p>更新于: {selectedTask.updatedAt}</p>
                    </div>
                </div>
            </div>
        );
    };

    // 渲染创建/编辑表单
    const renderTaskForm = () => (
        <div className="h-full flex flex-col">
            <h3 className="text-xl font-semibold mb-4">{selectedTask ? '编辑任务' : '创建新任务'}</h3>
            <div className="space-y-4 flex-1">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">任务标题 *</label>
                    <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入任务标题"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">任务描述</label>
                    <textarea
                        name="remark"
                        value={formData.remark}
                        onChange={handleInputChange}
                        rows="3"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入任务描述"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">关联模板 *</label>
                    <select
                        name="templateId"
                        value={formData.templateId}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={templatesLoading}
                    >
                        <option value="">选择关联模板</option>
                        {templates.map(template => (
                            <option key={template.id} value={template.id}>
                                {template.title} - {template.remark}
                            </option>
                        ))}
                    </select>
                    {templatesLoading && (
                        <p className="text-sm text-gray-500 mt-1">加载模板列表中...</p>
                    )}
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Cron表达式 *</label>
                    <div className="space-y-2">
                        <input
                            type="text"
                            name="cornExp"
                            value={formData.cornExp}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                            placeholder="输入Cron表达式"
                        />
                        <div className="flex flex-wrap gap-2">
                            {cronPresets.map((preset, index) => (
                                <button
                                    key={index}
                                    type="button"
                                    onClick={() => setFormData(prev => ({ ...prev, cornExp: preset.value }))}
                                    className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                >
                                    {preset.label}
                                </button>
                            ))}
                        </div>
                        <p className="text-xs text-gray-500">
                            Cron表达式格式: 秒 分 时 日 月 周 (例: 0 0 9 * * ? 表示每天9点执行)
                        </p>
                    </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                    <button
                        onClick={handleCancel}
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                        取消
                    </button>
                    <button
                        onClick={handleSave}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                        保存
                    </button>
                </div>
            </div>
        </div>
    );

    // 主渲染函数
    return (
        <div className="h-full flex flex-col text-gray-800">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">任务管理</h2>
                {!isEditing && (
                    <button
                        onClick={handleCreateNew}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        创建新任务
                    </button>
                )}
            </div>

            {loading ? (
                <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
            ) : (
                <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
                    {/* 任务列表 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden">
                        <div className="p-4 border-b border-gray-200">
                            <h3 className="font-medium">任务列表</h3>
                        </div>
                        <div className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 280px)' }}>
                            {tasks.length === 0 ? (
                                <div className="p-4 text-center text-gray-500">
                                    暂无任务，点击"创建新任务"按钮添加
                                </div>
                            ) : (
                                <ul className="divide-y divide-gray-200">
                                    {tasks.map(task => (
                                        <li
                                            key={task.id}
                                            className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${selectedTask && selectedTask.id === task.id ? 'bg-blue-50' : ''}`}
                                            onClick={() => handleSelectTask(task)}
                                        >
                                            <h4 className="font-medium">{task.title}</h4>
                                            <p className="text-sm text-gray-500 mt-1 truncate">{task.remark || '无描述'}</p>
                                            <div className="text-xs text-gray-400 mt-2">
                                                <p>模板: {getTemplateName(task.templateId)}</p>
                                                <p>更新于: {task.updatedAt}</p>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    </div>

                    {/* 任务详情及编辑区 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden md:col-span-2 p-6">
                        {isEditing ? renderTaskForm() : renderTaskDetail()}
                    </div>
                </div>
            )}
        </div>
    );
}
