// ReportConfig.js - 简报配置组件（占位符）
function ReportConfig() {
    return (
        <div className="h-full flex flex-col text-gray-800">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">简报配置</h2>
            </div>
            
            <div className="flex-1 flex items-center justify-center">
                <div className="text-center text-gray-500">
                    <svg className="w-16 h-16 mb-4 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p className="text-lg">简报配置功能</p>
                    <p className="text-sm mt-2">此功能正在开发中...</p>
                </div>
            </div>
        </div>
    );
}
