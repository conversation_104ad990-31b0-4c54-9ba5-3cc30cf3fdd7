<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理系统测试</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入React和ReactDOM -->
    <script src="https://unpkg.com/react@18/umd/react.development.js" crossorigin></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js" crossorigin></script>
    <!-- 引入Babel用于JSX转换 -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // 模拟消息提示
        window.showMessage = {
            success: (msg) => alert('成功: ' + msg),
            error: (msg) => alert('错误: ' + msg),
            warning: (msg) => alert('警告: ' + msg),
            info: (msg) => alert('信息: ' + msg)
        };

        function CronValidator() {
            const [cronExp, setCronExp] = useState('0 0 9 * * ?');
            const [naturalInput, setNaturalInput] = useState('');
            const [validationError, setValidationError] = useState('');
            const [generating, setGenerating] = useState(false);

            // Cron表达式校验函数
            const validateCronExpression = (cronExp) => {
                if (!cronExp || !cronExp.trim()) {
                    return 'Cron表达式不能为空';
                }

                const parts = cronExp.trim().split(/\s+/);
                if (parts.length !== 6) {
                    return 'Cron表达式必须包含6个字段：秒 分 时 日 月 周';
                }

                const [second, minute, hour, day, month, week] = parts;
                
                if (!isValidCronField(second, 0, 59)) {
                    return '秒字段无效，应为0-59或*或*/n格式';
                }
                
                if (!isValidCronField(minute, 0, 59)) {
                    return '分钟字段无效，应为0-59或*或*/n格式';
                }
                
                if (!isValidCronField(hour, 0, 23)) {
                    return '小时字段无效，应为0-23或*或*/n格式';
                }
                
                if (day !== '?' && !isValidCronField(day, 1, 31)) {
                    return '日期字段无效，应为1-31或?或*或*/n格式';
                }
                
                if (!isValidCronField(month, 1, 12)) {
                    return '月份字段无效，应为1-12或*或*/n格式';
                }
                
                if (week !== '?' && !isValidWeekField(week)) {
                    return '周字段无效，应为1-7或MON-SUN或?或*格式';
                }

                if (day !== '?' && week !== '?') {
                    return '日期和周字段不能同时指定，其中一个必须为?';
                }

                return null;
            };

            const isValidCronField = (field, min, max) => {
                if (field === '*' || field === '?') return true;
                
                if (field.includes('*/')) {
                    const step = parseInt(field.split('*/')[1]);
                    return !isNaN(step) && step > 0 && step <= max;
                }
                
                if (field.includes('-')) {
                    const [start, end] = field.split('-').map(n => parseInt(n));
                    return !isNaN(start) && !isNaN(end) && start >= min && end <= max && start <= end;
                }
                
                if (field.includes(',')) {
                    const values = field.split(',').map(n => parseInt(n));
                    return values.every(v => !isNaN(v) && v >= min && v <= max);
                }
                
                const num = parseInt(field);
                return !isNaN(num) && num >= min && num <= max;
            };

            const isValidWeekField = (field) => {
                if (field === '*' || field === '?') return true;
                
                const weekNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
                
                if (weekNames.includes(field.toUpperCase())) return true;
                
                const num = parseInt(field);
                if (!isNaN(num) && num >= 1 && num <= 7) return true;
                
                if (field.includes('-') || field.includes(',')) {
                    return field.split(/[-,]/).every(part => {
                        const trimmed = part.trim();
                        return weekNames.includes(trimmed.toUpperCase()) || 
                               (!isNaN(parseInt(trimmed)) && parseInt(trimmed) >= 1 && parseInt(trimmed) <= 7);
                    });
                }
                
                return false;
            };

            const handleCronChange = (e) => {
                const value = e.target.value;
                setCronExp(value);
                const error = validateCronExpression(value);
                setValidationError(error || '');
            };

            const generateCron = async () => {
                if (!naturalInput.trim()) {
                    window.showMessage.warning('请输入自然语言描述');
                    return;
                }

                setGenerating(true);
                // 模拟API调用
                setTimeout(() => {
                    // 简单的规则匹配示例
                    let generated = '0 0 9 * * ?'; // 默认每天9点
                    
                    if (naturalInput.includes('每小时')) {
                        generated = '0 0 * * * ?';
                    } else if (naturalInput.includes('每30分钟')) {
                        generated = '0 */30 * * * ?';
                    } else if (naturalInput.includes('每周一')) {
                        generated = '0 0 9 ? * MON';
                    } else if (naturalInput.includes('每月1号')) {
                        generated = '0 0 9 1 * ?';
                    } else if (naturalInput.includes('18点') || naturalInput.includes('下午6点')) {
                        generated = '0 0 18 * * ?';
                    }

                    setCronExp(generated);
                    const error = validateCronExpression(generated);
                    setValidationError(error || '');
                    setNaturalInput('');
                    setGenerating(false);
                    window.showMessage.success('Cron表达式生成成功！');
                }, 1000);
            };

            const presets = [
                { label: '每天9点', value: '0 0 9 * * ?' },
                { label: '每天18点', value: '0 0 18 * * ?' },
                { label: '每周一9点', value: '0 0 9 ? * MON' },
                { label: '每月1号9点', value: '0 0 9 1 * ?' },
                { label: '每小时', value: '0 0 * * * ?' },
                { label: '每30分钟', value: '0 */30 * * * ?' }
            ];

            return (
                <div className="min-h-screen flex items-center justify-center p-6">
                    <div className="glass rounded-xl p-8 max-w-2xl w-full">
                        <h1 className="text-3xl font-bold text-white mb-8 text-center">
                            Cron表达式智能生成与校验测试
                        </h1>
                        
                        {/* 智能生成区域 */}
                        <div className="bg-blue-50 p-4 rounded-md border border-blue-200 mb-6">
                            <label className="block text-sm font-medium text-blue-700 mb-2">智能生成</label>
                            <div className="flex gap-2">
                                <input
                                    type="text"
                                    value={naturalInput}
                                    onChange={(e) => setNaturalInput(e.target.value)}
                                    className="flex-1 px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="例：每天上午9点执行、每周一下午3点执行、每月1号执行"
                                    disabled={generating}
                                />
                                <button
                                    onClick={generateCron}
                                    disabled={generating || !naturalInput.trim()}
                                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center"
                                >
                                    {generating ? '生成中...' : '智能生成'}
                                </button>
                            </div>
                        </div>

                        {/* Cron表达式输入 */}
                        <div className="mb-6">
                            <label className="block text-sm font-medium text-white mb-2">Cron表达式</label>
                            <input
                                type="text"
                                value={cronExp}
                                onChange={handleCronChange}
                                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 font-mono ${
                                    validationError 
                                        ? 'border-red-300 focus:ring-red-500 bg-red-50' 
                                        : 'border-gray-300 focus:ring-blue-500'
                                }`}
                                placeholder="输入Cron表达式"
                            />
                            {validationError && (
                                <p className="text-red-500 text-sm mt-1 bg-red-50 p-2 rounded">{validationError}</p>
                            )}
                            {!validationError && cronExp && (
                                <p className="text-green-600 text-sm mt-1 bg-green-50 p-2 rounded">✓ Cron表达式格式正确</p>
                            )}
                        </div>

                        {/* 预设按钮 */}
                        <div className="mb-6">
                            <label className="block text-sm font-medium text-white mb-2">快速选择</label>
                            <div className="flex flex-wrap gap-2">
                                {presets.map((preset, index) => (
                                    <button
                                        key={index}
                                        onClick={() => {
                                            setCronExp(preset.value);
                                            setValidationError('');
                                        }}
                                        className="px-3 py-1 text-sm bg-white bg-opacity-20 text-white rounded hover:bg-opacity-30 transition-all"
                                    >
                                        {preset.label}
                                    </button>
                                ))}
                            </div>
                        </div>

                        <p className="text-white text-sm opacity-80 text-center">
                            Cron表达式格式: 秒 分 时 日 月 周 (例: 0 0 9 * * ? 表示每天9点执行)
                        </p>
                    </div>
                </div>
            );
        }

        ReactDOM.createRoot(document.getElementById('root')).render(<CronValidator />);
    </script>
</body>
</html>
